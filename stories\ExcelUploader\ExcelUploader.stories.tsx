import type { Meta } from "@storybook/react";
import type { StoryObj } from "@storybook/react";
import ExcelUploader from "./ExcelUploader";

const meta: Meta<typeof ExcelUploader> = {
  title: "ExcelUploader",
  tags: ["autodocs"],
  component: ExcelUploader,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof ExcelUploader>;
export const Primary: Story = {
  args: {
    visible: true,
    columnConfig: [
      {
        key: "deviceName",
        title: "车牌号",
        required: true,
        type: "string",
      },
      {
        key: "serialNo",
        title: "车架号",
        type: "string",
        required: true,
      },
      {
        key: "provinceName",
        title: "省份",
        type: "string",
        required: true,
      },
      {
        key: "cityName",
        title: "城市",
        type: "string",
      },
      {
        key: "stationName",
        title: "站点",
        type: "string",
      },
      {
        key: "radius",
        title: "运营半径",
        type: "number",
        transform: (value: any) => {
          if (!value) {
            return null;
          }
          return Number(value);
        },
      },
    ],
    uploadToS3: true,
    showStepGuidance: true,
    downloadIconText: "",
    uploadMode: "dragger",
    s3Config: {
      getPreSignatureUrl: "https://uat-api-cloud.jdl.cn/k2/oss/upload",
      maxFileSize: 50,
      unit: "MB",
      bucketName: "rover-operation",
      LOPDN: "device.web.public.jsf.beta",
    },
    description: "",
    onS3UploadConfirm: (file: any) => {
      console.log("file==", file);
    },
  },
};
